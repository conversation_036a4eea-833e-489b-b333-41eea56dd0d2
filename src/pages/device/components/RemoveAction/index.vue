<template>
  <EleTooltipButton
    type="danger"
    text
    :loading
    :icon="loading ? '' : 'Delete'"
    placement="top"
    :content="$t('common.remove')"
    @click="handleClick(device)"
  >
  </EleTooltipButton>

  <!-- 配置迁移对话框 -->
  <ConfigMigrationDialog
    ref="configMigrationDialog"
    @success="onMigrationSuccess"
    @skip="onMigrationSkip"
    @cancel="onMigrationCancel"
  />
</template>

<script setup>
import { ref } from 'vue'
import { sleep } from '$/utils'
import { findTargetDevices } from '$/utils/device'
import { ScrcpyConfigMigrator } from '$/store/device/helpers/index.js'
import ConfigMigrationDialog from './ConfigMigrationDialog.vue'

// Props
const props = defineProps({
  device: {
    type: Object,
    default: () => ({}),
  },
  handleRefresh: {
    type: Function,
    default: () => {},
  },
})

// Store
const deviceStore = useDeviceStore()

// Refs
const loading = ref(false)
const configMigrationDialog = ref(null)

/**
 * 处理点击删除按钮
 */
async function handleClick(device = props.device) {
  loading.value = true

  try {
    // 检查设备是否有 scrcpy 配置
    const migrator = new ScrcpyConfigMigrator()
    const scrcpyConfig = migrator.getScrcpyConfig()
    const hasConfig = migrator.hasConfig(scrcpyConfig, device.id)

    if (hasConfig) {
      // 如果有配置，先检查是否有可迁移的目标设备
      const targetDevices = findTargetDevices(device, deviceStore.list)

      if (targetDevices.length > 0) {
        // 有可迁移的目标设备，显示迁移对话框
        loading.value = false
        configMigrationDialog.value.open(device)
      }
      else {
        // 没有可迁移的目标设备，直接删除
        await removeDevice(device)
      }
    }
    else {
      // 如果没有配置，直接删除
      await removeDevice(device)
    }
  }
  catch (error) {
    console.error('Error checking device config:', error)
    // 出错时直接删除设备
    await removeDevice(device)
  }
}

/**
 * 删除设备
 */
async function removeDevice(device) {
  const devices = { ...window.appStore.get('device') }
  delete devices[device.id]
  window.appStore.set('device', devices)
  props.handleRefresh()
  await sleep()
  loading.value = false
}

/**
 * 配置迁移成功回调
 */
async function onMigrationSuccess() {
  await removeDevice(props.device)
}

/**
 * 跳过配置迁移回调
 */
async function onMigrationSkip() {
  await removeDevice(props.device)
}

/**
 * 取消配置迁移回调
 */
function onMigrationCancel() {
  loading.value = false
}
</script>

<style></style>
