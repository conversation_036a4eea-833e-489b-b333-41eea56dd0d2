{"common.empty": "暫無資料", "common.yes": "是", "common.no": "否", "common.cancel": "取消", "common.confirm": "確認", "common.restart": "重新啟動", "common.default": "預設", "common.tips": "提示", "common.open": "開啟", "common.input.placeholder": "請輸入", "common.success": "操作成功", "common.success.batch": "批次操作成功", "common.fail": "操作失敗", "common.starting": "啟動中", "common.loading": "載入中", "common.search": "搜尋", "common.batch": "批次", "common.device": "裝置", "common.progress": "進行中", "common.finished": "已結束", "common.stop": "終止", "common.remove": "移除", "common.select.please": "請選擇", "common.required": "該選項不能為空", "common.download": "下載", "common.downloading": "正在下載中", "common.delete": "刪除", "common.name": "名稱", "common.size": "大小", "common.warning": "警告", "common.info": "訊息", "common.danger": "錯誤", "common.connecting": "連線中", "common.language.name": "語言", "common.language.placeholder": "選擇你要的語言", "time.update": "更新時間", "time.unit.month": "月", "time.unit.week": "週", "time.unit.day": "天", "time.unit.hour": "小時", "time.unit.minute": "分鐘", "time.unit.second": "秒", "time.unit.millisecond": "毫秒", "appClose.name": "關閉主面板", "appClose.question": "每次詢問", "appClose.minimize": "最小化到工具列", "appClose.quit": "直接結束", "appClose.quit.cancel": "取消結束", "appClose.quit.loading": "正在停止服務...", "appClose.message": "確定要結束嗎？", "appClose.remember": "是否記住選擇？", "dependencies.lack.title": "注意事項", "dependencies.lack.content": "此軟體需要 {name} 來運作。請確保已正確安裝所需的工具，或在偏好設定中手動設定工具的位置。", "device.list": "裝置列表", "device.list.empty": "未偵測到裝置", "device.serial": "裝置標識", "device.name": "裝置名稱", "device.remark": "備註", "device.permission.error": "裝置權限錯誤，請重新連線裝置並允許 USB 偵錯", "device.terminal.name": "終端偵錯", "device.status": "狀態", "device.status.connected": "已連線", "device.status.offline": "已離線", "device.status.unauthorized": "未授權", "device.battery": "裝置電量", "device.isCharging": "充電狀態", "device.temperature": "裝置溫度", "device.powerSource": "驅動來源", "device.voltage": "裝置電壓", "device.config.migration.title": "配置遷移", "device.config.migration.description.title": "檢測到該裝置存在 scrcpy 配置", "device.config.migration.description.content": "是否將該配置遷移到目前裝置列表中同一裝置的條目？這將幫助您保留之前的設定。", "device.config.migration.select.title": "請選擇要遷移配置的目標裝置：", "device.config.migration.no.target.devices": "未找到可遷移的目標裝置", "device.config.migration.skip": "跳過", "device.config.migration.start": "開始遷移", "device.config.migration.success": "成功遷移 {count} 個裝置的配置", "device.config.migration.no.migration": "沒有配置需要遷移", "device.config.migration.error": "配置遷移失敗", "device.task.name": "計劃任務", "device.task.tips": "注意：請確保您的電腦保持喚醒狀態，否則計劃任務將無法正常執行。", "device.task.list": "任務列表", "device.task.type": "任務類型", "device.task.frequency": "執行頻率", "device.task.frequency.timeout": "單次執行", "device.task.frequency.interval": "週期重複", "device.task.timeout": "執行時間", "device.task.timeout.tips": "不能小於目前時間", "device.task.timeout.expired": "該任務已過期", "device.task.interval": "重複間隔", "device.task.devices": "涉及裝置", "device.task.noRepeat": "不重複", "device.task.restart": "再次執行", "device.task.extra.app": "選擇應用程式", "device.task.extra.shell": "選擇腳本", "device.wireless.name": "無線", "device.wireless.mode": "無線模式", "device.wireless.mode.error": "未取得區域網路連線位址，請檢查網路", "device.wireless.connect.qr": "二維碼連線", "device.wireless.connect.qr.pairing": "配對中", "device.wireless.connect.qr.connecting": "連線中", "device.wireless.connect.qr.connecting-fallback": "連線中", "device.wireless.connect.qr.connected": "連線成功", "device.wireless.connect.qr.error": "二維碼連線", "device.wireless.connect.name": "連線裝置", "device.wireless.connect.error.title": "連線裝置失敗", "device.wireless.connect.error.detail": "錯誤詳細資訊", "device.wireless.connect.error.reasons[0]": "可能有以下原因：", "device.wireless.connect.error.reasons[1]": "IP 位址或連接埠錯誤", "device.wireless.connect.error.reasons[2]": "裝置未與目前電腦配對成功", "device.wireless.connect.error.reasons[3]": "電腦網路和提供的裝置網路 IP 不在同一個區域網中", "device.wireless.connect.error.reasons[4]": "adb 路徑錯誤", "device.wireless.connect.error.reasons[5]": "其他未知錯誤", "device.wireless.connect.error.confirm": "無線配對", "device.wireless.connect.error.cancel": "@:common.cancel", "device.wireless.connect.error.no-address": "無線偵錯位址不能為空", "device.wireless.connect.success": "連線裝置成功", "device.wireless.connect.batch.name": "連線所有歷史裝置", "device.wireless.disconnect.start": "斷開連線", "device.wireless.disconnect.progress": "正在斷開", "device.wireless.disconnect.success": "斷開連線成功", "device.wireless.pair": "無線配對", "device.wireless.pair.tips": "注意：可以在 開發者選項 -> 無線偵錯 (可以點進去) -> 使用配對碼配對裝置中取得以下資訊", "device.wireless.pair.address": "配對 IP 位址", "device.wireless.pair.address.message": "配對碼不能為空", "device.wireless.pair.address.placeholder": "請輸入配對 IP 位址", "device.wireless.pair.port": "配對連接埠", "device.wireless.pair.port.message": "配對連接埠不能為空", "device.wireless.pair.port.placeholder": "請輸入配對連接埠", "device.wireless.pair.code": "配對碼", "device.wireless.pair.code.message": "配對碼不能為空", "device.wireless.pair.code.placeholder": "請輸入配對碼", "device.refresh.name": "重新整理裝置", "device.restart.name": "重啟服務", "device.log.name": "執行日誌", "device.arrange.name": "視窗編排", "device.arrange.dialog.title": "視窗編排", "device.arrange.widget.add": "新增元件", "device.arrange.widget.global": "全域設定", "device.arrange.layout.reset": "重設版面", "device.arrange.layout.clear": "清除全部", "device.arrange.layout.save": "儲存版面", "device.arrange.widget.size": "尺寸", "device.arrange.widget.position": "位置", "device.arrange.save.noChanges": "沒有變更需要儲存", "device.arrange.save.success": "已成功儲存 {count} 個元件的版面{removed}", "device.arrange.save.removed": "，移除了 {count} 個", "device.arrange.widget.global.exists": "全域元件已存在", "device.arrange.widget.global.name": "全域", "device.arrange.device.notFound": "裝置未找到", "device.arrange.clear.confirm": "確定要清除所有元件嗎？", "device.arrange.clear.title": "確認", "device.arrange.clear.success": "已清除所有元件", "device.mirror.start": "開始鏡像", "device.record.progress": "正在錄製", "device.record.success.title": "錄製成功", "device.record.success.message": "是否前往錄製位置進行檢視？", "device.actions.more.name": "更多操作", "device.actions.more.record.name": "開始錄製", "device.actions.more.camera.name": "啟動鏡頭", "device.actions.more.recordCamera.name": "錄製鏡頭", "device.actions.more.recordAudio.name": "錄製音訊", "device.actions.more.otg.name": "啟動 OTG", "device.actions.more.custom.name": "靈活啟動", "device.control.name": "操作", "device.control.more": "裝置互動", "device.control.install": "安裝 APP", "device.control.install.placeholder": "請選擇要安裝的應用程式", "device.control.install.progress": "正在為 {<PERSON><PERSON><PERSON>} 安裝應用程式中...", "device.control.install.success": "已成功將應用程式安裝到 {deviceName} 中，共 {totalCount} 個，成功 {successCount} 個，失敗 {failCount} 個", "device.control.install.success.single": "已成功將應用程式安裝到 {deviceName} 中", "device.control.install.error": "安裝應用程式失敗，請檢查安裝檔後重試", "device.control.file.name": "檔案管理", "device.control.file.push": "推送檔案", "device.control.file.push.placeholder": "請選擇要推送的檔案", "device.control.file.push.loading": "推送檔案中", "device.control.file.push.success.name": "推送檔案成功", "device.control.file.push.success": "已成功將 {totalCount} 個檔案推送到 {deviceName}，{successCount} 成功，{failCount} 失敗。", "device.control.file.push.success.single": "檔案已成功推送到 {deviceName}", "device.control.file.push.error": "推送檔案失敗，請檢查檔案後重試", "device.control.file.manager.storage": "內部儲存空間", "device.control.file.manager.add": "新增資料夾", "device.control.file.manager.upload": "上傳檔案", "device.control.file.manager.upload.directory": "上傳目錄", "device.control.file.manager.download": "下載檔案", "device.control.file.manager.download.tips": "確定要下載所選內容嗎？", "device.control.file.manager.delete.tips": "確定要刪除所選內容嗎？", "device.control.terminal.command.name": "執行命令", "device.control.terminal.script.name": "執行腳本", "device.control.terminal.script.select": "請選擇要執行的腳本", "device.control.terminal.script.push.loading": "推送腳本中...", "device.control.terminal.script.push.success": "推送腳本成功", "device.control.terminal.script.enter": "請輸入回車鍵確認執行該腳本", "device.control.terminal.script.success": "腳本執行成功", "device.control.capture": "擷取螢幕", "device.control.capture.progress": "正在擷取 {deviceName} 的螢幕快照...", "device.control.capture.success.message": "是否前往截圖位置進行檢視？", "device.control.capture.success.message.title": "截圖成功", "device.control.reboot": "重啟裝置", "device.control.turnScreenOff": "關閉螢幕", "device.control.turnScreenOff.tips": "關閉螢幕且保持控制（實驗功能）：此操作將建立一個 EscrcpyHelper 程序，手動關閉該程序將重新開啟螢幕。", "device.control.startApp": "啟動 APP", "device.control.startApp.useMainScreen": "使用主顯示器開啟", "device.control.power": "電源鍵", "device.control.power.tips": "可以用來開啟或關閉螢幕", "device.control.notification": "通知欄", "device.control.notification.tips": "開啟下拉選單選項", "device.control.return": "返回鍵", "device.control.home": "主螢幕", "device.control.switch": "切換鍵", "device.control.gnirehtet": "反向網路分享", "device.control.gnirehtet.tips": "使用 Gnirehtet 為 Android 提供反向網路共享；注意：首次連線需要在裝置上進行授權", "device.control.gnirehtet.start": "啟動服務", "device.control.gnirehtet.start.success": "Gnirehtet 反向網路共享功能啟動成功", "device.control.gnirehtet.stop": "停止服務", "device.control.gnirehtet.stop.success": "停止服務成功", "device.control.gnirehtet.running": "服務執行中", "device.control.gnirehtet.stopping": "服務停止中", "device.control.mirror-group.name": "多螢幕協同", "device.control.mirror-group.tips": "開啟後，可以同時鏡像多個模擬輔助顯示裝置，並透過操作各個鏡像視窗實現多螢幕協同功能。請注意，此功能需要手機 ROM 支援，並且必須開啟強制使用桌面模式選項。", "device.control.mirror-group.open": "開啟 {num} 個視窗", "device.control.mirror-group.close": "關閉輔助顯示裝置", "device.control.mirror-group.appClose.tips": "用於解決某些機型關閉所有控制視窗後自動關閉失敗的問題", "device.control.volume.name": "音量控制", "device.control.volume-up.name": "增加音量", "device.control.volume-down.name": "減小音量", "device.control.volume-mute.name": "關閉音量", "device.control.rotation.name": "旋轉螢幕", "device.control.rotation.vertically": "縱向旋轉", "device.control.rotation.horizontally": "橫向旋轉", "device.control.rotation.auto": "自動旋轉", "device.control.rotation.disable": "停用旋轉", "preferences.name": "偏好設定", "preferences.reset": "恢復預設值", "preferences.scope.global": "全域性", "preferences.scope.placeholder": "偏好設定的作用域範圍", "preferences.scope.details[0]": "對全域性或者單個裝置設定不同的偏好設定", "preferences.scope.details[1]": "全域性：將對所有裝置生效。", "preferences.scope.details[2]": "單個裝置：繼承於全域性設定，並對單個裝置進行獨立設定，僅對此裝置生效。", "preferences.config.import.name": "匯入", "preferences.config.import.placeholder": "請選擇要匯入的設定檔", "preferences.config.import.success": "匯入成功", "preferences.config.export.name": "匯出", "preferences.config.export.message": "匯出設定", "preferences.config.export.placeholder": "請選擇要匯出的位置", "preferences.config.export.success": "匯出成功", "preferences.config.edit.name": "編輯", "preferences.config.reset.name": "重設", "preferences.config.reset.tips": "注意：重設後，之前儲存的設定將會被清除，因此建議在執行重設操作之前備份您的設定。", "preferences.common.name": "一般設定", "preferences.common.theme.name": "主題", "preferences.common.theme.placeholder": "設定主題", "preferences.common.theme.options[0]": "淺色模式", "preferences.common.theme.options[1]": "深色模式", "preferences.common.theme.options[2]": "依照系統設定", "preferences.common.debug.name": "偵錯", "preferences.common.debug.placeholder": "是否啟動軟體偵錯", "preferences.common.debug.tips": "啟用後可以在執行日誌中檢視軟體執行情況，一般不需要開啟可能會影響效能，注意：改變此選項需要重啟軟體後才能生效。", "preferences.common.file.name": "檔案儲存路徑", "preferences.common.file.placeholder": "使用者桌面", "preferences.common.file.tips": "截圖和錄製的影像存放在這裡", "preferences.common.adb.name": "adb 路徑", "preferences.common.adb.placeholder": "自定義 adb 路徑", "preferences.common.adb.tips": "用於連線裝置的 adb 位址。", "preferences.common.scrcpy.name": "scrcpy 路徑", "preferences.common.scrcpy.placeholder": "自定義 scrcpy 路徑", "preferences.common.scrcpy.tips": "用於控制裝置的 scrcpy 位址。", "preferences.common.scrcpy.append.name": "scrcpy 參數", "preferences.common.scrcpy.append.placeholder": "為 scrcpy 命令加上額外的參數", "preferences.common.scrcpy.append.tips": "注意：錄入參數將會直接附加到 scrcpy 命令中，如果存在重複的參數，並不會自動進行過濾。", "preferences.common.gnirehtet.name": "gnirehtet 路徑", "preferences.common.gnirehtet.placeholder": "自定義 gnirehtet 路徑", "preferences.common.gnirehtet.tips": "用於為裝置反向網路分享的 gnirehtet 位址。", "preferences.common.gnirehtet.fix.name": "gnirehtet 修復", "preferences.common.gnirehtet.fix.placeholder": "開啟後，將禁用 gnirehtet.apk 的安裝檢查，這可能改善某些設備的連接問題", "preferences.common.gnirehtet.fix.tips": "注意：這可能會導致每次啟動時重新安裝 gnirehtet.apk。", "preferences.common.gnirehtet.append.name": "gnirehtet 參數", "preferences.common.gnirehtet.append.placeholder": "為 gnirehtet 命令加上額外的參數", "preferences.common.gnirehtet.append.tips": "注意：錄入參數將會直接附加到 gnirehtet 命令中，如果存在重複的參數，並不會自動進行過濾。", "preferences.common.floatControl.name": "浮動操控欄", "preferences.common.floatControl.placeholder": "啟用後，鏡像時將會自動開啟裝置浮動操控欄", "preferences.common.auto-connect.name": "自動連線裝置", "preferences.common.auto-connect.placeholder": "啟用後，該軟體將在啟動時嘗試自動連線到歷史無線裝置", "preferences.common.auto-mirror.name": "自動執行鏡像", "preferences.common.auto-mirror.placeholder": "啟用後，裝置列表中的裝置將自動執行鏡像", "preferences.common.edgeHidden.name": "主面板貼邊隱藏", "preferences.common.edgeHidden.placeholder": "啟用後，當滑鼠靠近螢幕邊緣離開面板時，主面板將自動隱藏", "preferences.common.edgeHidden.tips": "注意：更改此選項後，需要重啟應用程式才能生效", "preferences.common.imeFix.name": "啟動APP鍵盤修復", "preferences.common.imeFix.placeholder": "啟用後，將解決APP啟動時輸入法無法在當前鏡像視窗顯示的問題。", "preferences.common.imeFix.tips": "注意：此功能僅支援 scrcpy v3.2 及以上版本，低版本使用時會報錯。", "preferences.video.name": "影片控制", "preferences.video.disable-video.name": "停用影片轉發", "preferences.video.disable-video.placeholder": "開啟後將停用影片轉發", "preferences.video.video-source.name": "影片來源", "preferences.video.video-source.placeholder": "預設為裝置顯示器", "preferences.video.video-source.display": "顯示器", "preferences.video.video-source.camera": "鏡頭", "preferences.video.resolution.name": "最大尺寸", "preferences.video.resolution.placeholder": "裝置尺寸，格式：1080", "preferences.video.bit.name": "影片位元率", "preferences.video.bit.placeholder": "8000000，格式：8M，8000000", "preferences.video.refresh-rate.name": "更新率", "preferences.video.refresh-rate.placeholder": "60", "preferences.video.video-code.name": "影片編碼", "preferences.video.video-code.placeholder": "h.264", "preferences.video.display-orientation.name": "顯示方向", "preferences.video.display-orientation.placeholder": "裝置顯示方向", "preferences.video.angle.name": "旋轉角度", "preferences.video.angle.placeholder": "不旋轉，格式：15", "preferences.video.angle.tips": "注意：此選項在錄製時同樣有效", "preferences.video.screen-cropping.name": "螢幕裁剪", "preferences.video.screen-cropping.placeholder": "不裁剪，格式：1224:1440:0:0", "preferences.video.display.name": "顯示器", "preferences.video.display.placeholder": "裝置顯示器", "preferences.video.video-buffer.name": "影片緩衝", "preferences.video.video-buffer.placeholder": "0", "preferences.video.receiver-buffer.name": "接收器緩衝 (v412)", "preferences.video.receiver-buffer.placeholder": "0", "preferences.device.name": "裝置控制", "preferences.device.show-touch.name": "顯示觸控點", "preferences.device.show-touch.placeholder": "開啟後將啟用開發者選項中的顯示點按觸控回饋", "preferences.device.show-touch.tips": "僅在實體裝置上顯示", "preferences.device.stay-awake.name": "保持喚醒", "preferences.device.stay-awake.placeholder": "開啟後將防止裝置進入睡眠狀態", "preferences.device.stay-awake.tips": "僅有線方式連線時有效", "preferences.device.turnScreenOff.name": "控制時關閉螢幕", "preferences.device.turnScreenOff.placeholder": "開啟後控制裝置時將自動關閉裝置螢幕", "preferences.device.screenOffTimeout.name": "螢幕超時", "preferences.device.screenOffTimeout.placeholder": "裝置預設", "preferences.device.screenOffTimeout.tips": "修改螢幕關閉超時設定，並在結束時恢復裝置預設", "preferences.device.control-end-video.name": "控制結束關閉螢幕", "preferences.device.control-end-video.placeholder": "開啟後停止控制裝置將自動關閉裝置螢幕", "preferences.device.control-in-stop-charging.name": "控制時停用自動亮螢幕", "preferences.device.control-in-stop-charging.placeholder": "開啟後控制裝置時將停用自動亮螢幕", "preferences.device.control-in-stop-charging.tips": "開啟後控制裝置時將停用自動亮螢幕", "preferences.device.display-overlay.name": "模擬輔助顯示器", "preferences.device.display-overlay.placeholder": "裝置大小，格式：1920x1080/420，1920x1080，/240", "preferences.device.display-overlay.tips": "用於調整模擬輔助顯示器的大小和解析度，啟動應用程式、多螢幕協同（鏡像組）依賴於此選項", "preferences.window.name": "視窗控制", "preferences.window.borderless.name": "無邊框模式", "preferences.window.borderless.placeholder": "開啟後控制視窗將變為無邊框模式", "preferences.window.full-screen.name": "全螢幕模式", "preferences.window.full-screen.placeholder": "開啟後控制視窗將全螢幕顯示模式", "preferences.window.always-top.name": "始終位於頂端", "preferences.window.always-top.placeholder": "開啟後控制視窗將始終位於頂端", "preferences.window.disable-screen-saver.name": "停用螢幕保護程式", "preferences.window.disable-screen-saver.placeholder": "開啟後將停用電腦螢幕保護程式", "preferences.window.size.width": "視窗寬度", "preferences.window.size.width.placeholder": "裝置寬度", "preferences.window.size.width.tips": "注意：更改此設定可能會導致顯示模糊", "preferences.window.size.height": "視窗高度", "preferences.window.size.height.placeholder": "裝置高度", "preferences.window.size.height.tips": "注意：更改此設定可能會導致顯示模糊", "preferences.window.position.x": "視窗橫座標", "preferences.window.position.x.placeholder": "相對於桌面中心", "preferences.window.position.y": "視窗縱座標", "preferences.window.position.y.placeholder": "相對於桌面中心", "preferences.record.name": "影片錄製", "preferences.record.format.name": "影片格式", "preferences.record.format.placeholder": "mp4", "preferences.record.format.audio.name": "音訊格式", "preferences.record.format.audio.placeholder": "opus", "preferences.record.time-limit.name": "錄製時長", "preferences.record.time-limit.placeholder": "不限時長", "preferences.record.orientation.name": "錄製影片方向", "preferences.record.orientation.placeholder": "裝置預設方向", "preferences.record.no-video-playback.name": "停用影片播放", "preferences.record.no-video-playback.placeholder": "開啟後錄製時將停用影片播放", "preferences.record.no-video-playback.tips": "注意：只是停用了播放但是依然會錄製影片", "preferences.record.no-audio-playback.name": "停用音訊播放", "preferences.record.no-audio-playback.placeholder": "開啟後錄製時將停用音訊播放", "preferences.record.no-audio-playback.tips": "注意：只是停用了播放但是依然會錄製音訊", "preferences.audio.name": "音訊控制", "preferences.audio.disable-audio.name": "停用音訊轉發", "preferences.audio.disable-audio.placeholder": "開啟後將停用音訊轉發", "preferences.audio.disable-audio.tips": "如果您的裝置音訊捕獲異常，則可以開啟此選項，以確保可以正常開啟鏡像", "preferences.audio.audioDup.name": "保持裝置音訊", "preferences.audio.audioDup.placeholder": "開啟後鏡像時將保持音訊在裝置上播放", "preferences.audio.audioDup.tips": "注意：此選項需要 Android 13+，且應用程式可選擇結束（這種情況下它們將不會被捕獲）", "preferences.audio.audio-source.name": "音訊來源", "preferences.audio.audio-source.placeholder": "裝置音訊輸出", "preferences.audio.audio-source.tips": "技巧：如果將來源設為麥克風將可以在錄製時將聲音錄製下來", "preferences.audio.audio-source.output": "裝置輸出", "preferences.audio.audio-source.mic": "麥克風", "preferences.audio.audio-source.playback": "捕獲音訊播放（Android 應用程式可以選擇退出，因此不一定捕獲整個輸出）", "preferences.audio.audio-source.mic-unprocessed": "捕獲麥克風未處理的（原始）聲音", "preferences.audio.audio-source.mic-camcorder": "捕獲針對視頻錄製而調諧的麥克風，如果可用，其方向與攝像頭相同", "preferences.audio.audio-source.mic-voice-recognition": "捕獲針對語音識別進行調節的麥克風", "preferences.audio.audio-source.mic-voice-communication": "捕獲針對語音通信進行調諧的麥克風（例如，如果可用，它將利用回聲消除或自動增益控制）", "preferences.audio.audio-source.voice-call": "捕獲語音呼叫", "preferences.audio.audio-source.voice-call-uplink": "僅捕獲語音呼叫上行鏈路", "preferences.audio.audio-source.voice-call-downlink": "僅捕獲語音呼叫下行鏈路", "preferences.audio.audio-source.voice-performance": "捕獲用於現場表演（卡拉 OK）的音訊，包括麥克風和設備播放", "preferences.audio.audio-code.name": "音訊編碼", "preferences.audio.audio-code.placeholder": "opus", "preferences.audio.audio-bit-rate.name": "音訊位元率", "preferences.audio.audio-bit-rate.placeholder": "128000，格式：128K，128000", "preferences.audio.audio-bit-rate.tips": "注意：此選項不適用於 RAW 音訊編解碼器", "preferences.audio.audio-buffer.name": "音訊緩衝", "preferences.audio.audio-buffer.placeholder": "0", "preferences.audio.audio-output-buffer.name": "音訊輸出緩衝", "preferences.audio.audio-output-buffer.placeholder": "5", "preferences.input.name": "輸入控制", "preferences.input.mouse.name": "滑鼠模式", "preferences.input.mouse.tips": "設定滑鼠輸入模式", "preferences.input.mouse.placeholder": "sdk", "preferences.input.mouse.options[0].placeholder": "預設", "preferences.input.mouse.options[1].placeholder": "使用裝置上的 UHID 核心模組模擬物理 HID 滑鼠", "preferences.input.mouse.options[2].placeholder": "使用 AOAv2 協議模擬物理 HID 滑鼠", "preferences.input.mouse.options[3].placeholder": "停用滑鼠輸入", "preferences.input.mouse-bind.name": "滑鼠綁定", "preferences.input.mouse-bind.tips": "該選項允許自定義滑鼠按鍵功能。它使用兩組 4 字元序列來定義主要和次要 (Shift 鍵) 綁定。每個字元代表一個滑鼠按鍵 (右鍵、中鍵、第 4 鍵、第 5 鍵),可以設定為:'+' 轉發到裝置，'-' 忽略，'b' 返回，'h' 主頁，'s' 切換應用程式，'n' 展開通知面板。例如，--mouse-bind=bhsn:++++ 表示主要綁定為 返回/主頁/切換應用程式/通知，次要綁定全部轉發到裝置。", "preferences.input.mouse-bind.placeholder": "bhsn:++++", "preferences.input.keyboard.name": "鍵盤模式", "preferences.input.keyboard.tips": "設定鍵盤輸入模式", "preferences.input.keyboard.placeholder": "sdk", "preferences.input.keyboard.options[0].placeholder": "預設", "preferences.input.keyboard.options[1].placeholder": "使用裝置上的 UHID 核心模組模擬物理 HID 鍵盤", "preferences.input.keyboard.options[2].placeholder": "使用 AOAv2 協議模擬物理 HID 鍵盤", "preferences.input.keyboard.options[3].placeholder": "停用鍵盤輸入", "preferences.input.keyboard.inject.name": "鍵盤輸入方式", "preferences.input.keyboard.inject.placeholder": "預設", "preferences.input.keyboard.inject.tips": "設定鍵盤文字輸入優先選項", "preferences.input.keyboard.inject.options[0].placeholder": "將字母作為文字輸入", "preferences.input.keyboard.inject.options[1].placeholder": "強制始終輸入原始按鍵事件", "preferences.input.gamepad.name": "遊戲手把", "preferences.input.gamepad.placeholder": "停用", "preferences.input.gamepad.tips": "此選項允許將遊戲手把（PS4/PS5 或 Xbox）連線到您的電腦以玩安卓遊戲。注意：所玩的遊戲必須支援遊戲手把輸入。", "preferences.camera.name": "鏡頭控制", "preferences.camera.camera-facing.name": "鏡頭來源", "preferences.camera.camera-facing.placeholder": "裝置鏡頭來源", "preferences.camera.camera-facing.front": "前置鏡頭", "preferences.camera.camera-facing.back": "後置鏡頭", "preferences.camera.camera-facing.external": "外接鏡頭", "preferences.camera.camera-size.name": "鏡頭解析度", "preferences.camera.camera-size.placeholder": "裝置鏡頭解析度，格式：1920x1080", "preferences.camera.camera-ar.name": "鏡頭比例", "preferences.camera.camera-ar.placeholder": "裝置鏡頭比例，格式：4:3，sensor，1.6", "preferences.camera.camera-fps.name": "鏡頭更新率", "preferences.camera.camera-fps.placeholder": "裝置鏡頭更新率", "about.name": "關於", "about.description": "📱 使用圖形化的 Scrcpy 顯示和控制您的 Android 裝置，使用 Electron 開發", "about.update": "檢查並更新", "about.update-not-available": "已經是最新版本", "about.update-error.title": "檢查更新失敗", "about.update-error.message": "你可能需要科學上網，是否前往釋出頁面手動下載更新？", "about.update-downloaded.title": "下載新版本成功", "about.update-downloaded.message": "是否立即重新啟動以更新？", "about.update-downloaded.confirm": "更新", "about.update-available.title": "發現新版本", "about.update-available.confirm": "更新", "about.update.progress": "正在更新", "about.donate.title": "捐贈", "about.donate.description": "如果覺得這個專案有幫到您的話，可以考慮請我喝杯咖啡，讓我更有精神持續改進此專案 😛", "about.docs.name": "幫助文件", "desktop.shortcut.add": "加入桌面快捷方式"}