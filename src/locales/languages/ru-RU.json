{"common.empty": "Нет данных", "common.yes": "Да", "common.no": "Нет", "common.cancel": "Отмена", "common.confirm": "Подтвердить", "common.restart": "Перезапустить", "common.default": "По умолчанию", "common.tips": "Советы", "common.open": "Открыть", "common.input.placeholder": "Пожалуйста, введите", "common.success": "Операция успешно выполнена", "common.success.batch": "Пакетная операция выполнена успешно", "common.fail": "Не удалось выполнить операцию", "common.starting": "Запуск", "common.loading": "Загрузка", "common.search": "Поиск", "common.batch": "Пак<PERSON>т", "common.device": "Устройство", "common.progress": "В процессе", "common.finished": "Завершено", "common.stop": "Остановить", "common.remove": "Удалить", "common.select.please": "Пожалуйста, выберите", "common.required": "Это поле не может быть пустым", "common.download": "Скачать", "common.downloading": "Загрузка", "common.delete": "Удалить", "common.name": "Имя", "common.size": "Размер", "common.warning": "Предупреждение", "common.info": "Сообщение", "common.danger": "Ошибка", "common.connecting": "Подключение", "common.language.name": "Язык", "common.language.placeholder": "Выберите язык", "time.update": "Время обновления", "time.unit.month": "мес<PERSON><PERSON>", "time.unit.week": "неделя", "time.unit.day": "день", "time.unit.hour": "час", "time.unit.minute": "минута", "time.unit.second": "секунда", "time.unit.millisecond": "миллисекунда", "appClose.name": "Закрыть главную панель", "appClose.question": "Спрашивать каждый раз", "appClose.minimize": "Свернуть в трей", "appClose.quit": "Выйти сразу", "appClose.quit.cancel": "Отменить выход", "appClose.quit.loading": "Остановка сервиса...", "appClose.message": "Вы уверены, что хотите выйти?", "appClose.remember": "Запомнить выбор?", "dependencies.lack.title": "Уведомление", "dependencies.lack.content": "Это программное обеспечение зависит от {name}. Пожалуйста, убедитесь, что указанные зависимости правильно установлены, или вручную настройте расположение зависимостей в настройках.", "device.list": "Устройства", "device.list.empty": "Устройства не обнаружены", "device.serial": "сериал", "device.name": "Имя", "device.remark": "Примечание", "device.permission.error": "Ошибка разрешения устройства, пожалуйста, переподключите устройство и разрешите отладку по USB", "device.terminal.name": "<PERSON>ер<PERSON><PERSON><PERSON><PERSON>", "device.status": "Статус", "device.status.connected": "Подключено", "device.status.authorizing": "Авторизация", "device.status.offline": "Не в сети", "device.status.unauthorized": "Не авторизован", "device.battery": "Уровень заряда устройства", "device.isCharging": "Состояние зарядки", "device.temperature": "Температура устройства", "device.powerSource": "Источник питания", "device.voltage": "Напряжение устройства", "device.task.name": "Запланированная задача", "device.task.tips": "Примечание: Пож<PERSON><PERSON><PERSON>йста, убеди<PERSON><PERSON><PERSON><PERSON>, что ваш компьютер не переходит в спящий режим, иначе запланированные задачи не будут выполнены правильно.", "device.task.list": "список задач", "device.task.type": "Ти<PERSON> задачи", "device.task.frequency": "Частота выполнения", "device.task.frequency.timeout": "Однократное выполнение", "device.task.frequency.interval": "Периодическое повторение", "device.task.timeout": "Время выполнения", "device.task.timeout.tips": "Не может быть раньше текущего времени", "device.task.timeout.expired": "Задача истекла", "device.task.interval": "Интервал повторения", "device.task.devices": "Устройство", "device.task.noRepeat": "Без повторения", "device.task.restart": "Выполнить снова", "device.task.extra.app": "Выберите приложение", "device.task.extra.shell": "Выберите скрипт", "device.wireless.name": "По WiFi", "device.wireless.mode": "беспроводной", "device.wireless.mode.error": "Не удалось получить адрес подключения локальной сети, проверьте сеть", "device.wireless.connect.qr": "Подключение по QR-коду", "device.wireless.connect.qr.pairing": "Сопряжение", "device.wireless.connect.qr.connecting": "Подключение", "device.wireless.connect.qr.connecting-fallback": "Подключение", "device.wireless.connect.qr.connected": "Подключение успешно", "device.wireless.connect.qr.error": "Подключение по QR-коду", "device.wireless.connect.name": "Подключиться", "device.wireless.connect.error.title": "Не удалось подключиться", "device.wireless.connect.error.detail": "Детали ошибки", "device.wireless.connect.error.reasons[0]": "Возможные причины:", "device.wireless.connect.error.reasons[1]": "Неправильный IP или порт", "device.wireless.connect.error.reasons[2]": "Устройство не спарено", "device.wireless.connect.error.reasons[3]": "IP не в одной подсети", "device.wireless.connect.error.reasons[4]": "Ошибка пути к adb", "device.wireless.connect.error.reasons[5]": "Другая неизвестная ошибка", "device.wireless.connect.error.confirm": "Беспроводное сопряжение", "device.wireless.connect.error.cancel": "@:common.cancel", "device.wireless.connect.error.no-address": "Адрес беспроводной отладки не может быть пустым", "device.wireless.connect.success": "Успешное подключение", "device.wireless.connect.batch.name": "Подключить все устройства из истории", "device.wireless.disconnect.start": "Отключить", "device.wireless.disconnect.progress": "Отключение", "device.wireless.disconnect.success": "Отключено", "device.wireless.pair": "Беспроводное сопряжение", "device.wireless.pair.tips": "Получите следующую информацию в Опции разработчика -> Беспроводная отладка -> Сопряжение устройства", "device.wireless.pair.address": "IP адрес для сопряжения", "device.wireless.pair.address.message": "Адрес для сопряжения не может быть пустым", "device.wireless.pair.address.placeholder": "Введите IP адрес для сопряжения", "device.wireless.pair.port": "Порт для сопряжения", "device.wireless.pair.port.message": "Порт для сопряжения не может быть пустым", "device.wireless.pair.port.placeholder": "Введите порт для сопряжения", "device.wireless.pair.code": "Код для сопряжения", "device.wireless.pair.code.message": "Код для сопряжения не может быть пустым", "device.wireless.pair.code.placeholder": "Введите код для сопряжения", "device.refresh.name": "Обновить", "device.restart.name": "Перезапустить", "device.log.name": "Логи", "device.arrange.name": "Расположение", "device.arrange.dialog.title": "Расположение окон", "device.arrange.widget.add": "Добавить виджет", "device.arrange.widget.global": "Глобальная конфигурация", "device.arrange.layout.reset": "Сбросить макет", "device.arrange.layout.clear": "Очистить все", "device.arrange.layout.save": "Сохранить макет", "device.arrange.widget.size": "Размер", "device.arrange.widget.position": "Позиция", "device.arrange.save.noChanges": "Нет изменений для сохранения", "device.arrange.save.success": "Макет успешно сохранен для {count} виджет(ов){removed}", "device.arrange.save.removed": ", {count} удалено", "device.arrange.widget.global.exists": "Глобальный виджет уже существует", "device.arrange.widget.global.name": "Глобальный", "device.arrange.device.notFound": "Устройство не найдено", "device.arrange.clear.confirm": "Вы уверены, что хотите очистить все виджеты?", "device.arrange.clear.title": "Подтвердить", "device.arrange.clear.success": "Все виджеты очищены", "device.mirror.start": "Подключиться", "device.record.progress": "Запись", "device.record.success.title": "Запись успешна", "device.record.success.message": "Открыть место сохранения записи?", "device.actions.more.name": "дополнительный", "device.actions.more.record.name": "Начать запись", "device.actions.more.camera.name": "Запустить камеры", "device.actions.more.recordCamera.name": "Запись камеры", "device.actions.more.recordAudio.name": "Записать аудио", "device.actions.more.otg.name": "Запустить OTG", "device.actions.more.custom.name": "Пользовательский запуск", "device.control.name": "Управление", "device.control.more": "Дополнительные элементы управления", "device.control.install": "Установить APP", "device.control.install.placeholder": "Выберите приложение для установки", "device.control.install.progress": "Установка приложения на {deviceName}...", "device.control.install.success": "Успешно установлено {totalCount} приложений на {deviceName}, успешно: {successCount}, неудачно: {failCount}", "device.control.install.success.single": "Приложение успешно установлено на {deviceName}", "device.control.install.error": "Ошибка установки, пожалуйста, проверьте приложение и попробуйте снова", "device.control.file.name": "Файловый менеджер", "device.control.file.push": "Отправить файл", "device.control.file.push.placeholder": "Пожалуйста, выберите файл для отправки", "device.control.file.push.loading": "Отправка файла", "device.control.file.push.success.name": "Файлы успешно отправлены", "device.control.file.push.success": "Успешно отправлено {totalCount} файлов на {deviceName}, успешно: {successCount}, неудачно: {failCount}", "device.control.file.push.success.single": "Файлы успешно отправлены на {deviceName}", "device.control.file.push.error": "Не удалось отправить файл, пожалуйста, проверьте файл и попробуйте снова", "device.control.file.manager.storage": "Внутреннее хранилище", "device.control.file.manager.add": "Новая папка", "device.control.file.manager.upload": "Загрузить файл", "device.control.file.manager.upload.directory": "Загрузить каталог", "device.control.file.manager.download": "Скачать файл", "device.control.file.manager.download.tips": "Вы уверены, что хотите скачать выбранный контент?", "device.control.file.manager.delete.tips": "Вы уверены, что хотите удалить выбранный контент?", "device.control.terminal.command.name": "Выполнить команду", "device.control.terminal.script.name": "Выполнить скрипт", "device.control.terminal.script.select": "Пожалуйста, выберите скрипт, который хотите выполнить", "device.control.terminal.script.push.loading": "Отправка скрипта...", "device.control.terminal.script.push.success": "Скрипт успешно отправлен", "device.control.terminal.script.enter": "Пожалуйста, нажмите Enter для подтверждения выполнения скрипта", "device.control.terminal.script.success": "Скрипт успешно выполнен", "device.control.capture": "Скриншот", "device.control.capture.progress": "Снимок экрана для {deviceName}...", "device.control.capture.success.message": "Открыть место сохранения скриншота?", "device.control.capture.success.message.title": "Скриншот успешно создан", "device.control.reboot": "Перезагрузить", "device.control.turnScreenOff": "Выключить экран", "device.control.turnScreenOff.tips": "Отключение экрана с сохранением контроля (экспериментально): это действие создаст процесс EscrcpyHelper; при ручном завершении этого процесса экран снова включится.", "device.control.startApp": "Запустить APP", "device.control.startApp.useMainScreen": "Открыть на основном экране", "device.control.power": "Питание", "device.control.power.tips": "Включить/выключить экран", "device.control.notification": "Уведомление", "device.control.notification.tips": "Открыть панель уведомлений", "device.control.return": "Назад", "device.control.home": "До<PERSON><PERSON><PERSON>", "device.control.switch": "Недавние", "device.control.gnirehtet": "Gnirehtet", "device.control.gnirehtet.tips": "Gnirehtet обеспечивает обратный тетеринг для Android; Примечание: Первоначальное подключение требует авторизации на устройстве.", "device.control.gnirehtet.start": "Запустить сервис", "device.control.gnirehtet.start.success": "Функция обратного тетеринга Gnirehtet успешно запущена", "device.control.gnirehtet.stop": "Остановить сервис", "device.control.gnirehtet.stop.success": "Сервис успешно остановлен", "device.control.gnirehtet.running": "Сервис запущен", "device.control.gnirehtet.stopping": "Остановка сервиса", "device.control.mirror-group.name": "Группа зеркалирования", "device.control.mirror-group.tips": "При включении можно отображать несколько эмулируемых вторичных дисплеев и осуществлять многопоточную работу, управляя каждым окном зеркалирования. Обратите внимание, что для этого требуется поддержка ROM и включенный режим рабочего стола.", "device.control.mirror-group.open": "Открыть {num} окон", "device.control.mirror-group.close": "Закрыть вспомогательные дисплеи", "device.control.mirror-group.appClose.tips": "Используется для решения проблемы автоматического закрытия после того, как некоторые устройства выходят из всех окон управления.", "device.control.volume.name": "Громкость", "device.control.volume-up.name": "Увеличить громкость", "device.control.volume-down.name": "Уменьшить громкость", "device.control.volume-mute.name": "Выключить звук", "device.control.rotation.name": "Поворот", "device.control.rotation.vertically": "Вертикально", "device.control.rotation.horizontally": "Горизонтально", "device.control.rotation.auto": "Авто", "device.control.rotation.disable": "Отключить", "preferences.name": "Настройки", "preferences.reset": "Сбросить на умолчания", "preferences.scope.global": "Глобальный", "preferences.scope.placeholder": "Область настроек", "preferences.scope.details[0]": "Установить глобальные или индивидуальные настройки для устройства", "preferences.scope.details[1]": "Глобальный: Применить ко всем устройствам", "preferences.scope.details[2]": "Индивидуальный: Переопределить глобальные настройки для одного устройства", "preferences.config.import.name": "Импорт", "preferences.config.import.placeholder": "Выберите файл конфигурации", "preferences.config.import.success": "Импорт успешно выполнен", "preferences.config.export.name": "Экспорт", "preferences.config.export.message": "Экспортировать конфигурацию", "preferences.config.export.placeholder": "Выберите место экспорта", "preferences.config.export.success": "Экспорт успешно выполнен", "preferences.config.edit.name": "Редактировать", "preferences.config.reset.name": "Сброс", "preferences.config.reset.tips": "Примечание: После сброса все ранее сохраненные настройки будут удалены, поэтому рекомендуется сделать резервную копию ваших настроек перед выполнением сброса.", "preferences.common.name": "Общие", "preferences.common.theme.name": "Тема", "preferences.common.theme.placeholder": "Установить тему", "preferences.common.theme.options[0]": "Светлая тема", "preferences.common.theme.options[1]": "Темная тема", "preferences.common.theme.options[2]": "Следовать системе", "preferences.common.debug.name": "Отладка", "preferences.common.debug.placeholder": "Включить режим отладки", "preferences.common.debug.tips": "Показывать информацию отладки в журнале, отключите для улучшения производительности. Требуется перезагрузка для вступления в силу.", "preferences.common.file.name": "Место сохранения файлов", "preferences.common.file.placeholder": "Рабочий стол пользователя", "preferences.common.file.tips": "Место для сохранения скриншотов и записей", "preferences.common.adb.name": "Путь к Adb", "preferences.common.adb.placeholder": "Пользовательский путь к adb", "preferences.common.adb.tips": "Путь к adb для подключения устройства", "preferences.common.scrcpy.name": "Путь к Scrcpy", "preferences.common.scrcpy.placeholder": "Пользовательский путь к scrcpy", "preferences.common.scrcpy.tips": "Путь к scrcpy для управления устройством", "preferences.common.scrcpy.append.name": "Аргументы Scrcpy", "preferences.common.scrcpy.append.placeholder": "Добавить дополнительные аргументы к команде scrcpy", "preferences.common.scrcpy.append.tips": "Примечание: Введенные аргументы будут непосредственно добавлены к команде scrcpy без фильтрации дублирующихся аргументов.", "preferences.common.gnirehtet.name": "Путь к Gnirehtet", "preferences.common.gnirehtet.placeholder": "Пользовательский путь к gnirehtet", "preferences.common.gnirehtet.tips": "Путь к gnirehtet, используемый для обеспечения обратного тетеринга для устройств.", "preferences.common.gnirehtet.fix.name": "Исправление Gnirehtet", "preferences.common.gnirehtet.fix.placeholder": "При включении отключает проверку установки gnirehtet.apk, что может улучшить проблемы с подключением на некоторых устройствах.", "preferences.common.gnirehtet.fix.tips": "Примечание: Это может привести к переустановке gnirehtet.apk при каждом запуске.", "preferences.common.gnirehtet.append.name": "Аргументы gnirehtet", "preferences.common.gnirehtet.append.placeholder": "Добавить дополнительные аргументы к команде gnirehtet", "preferences.common.gnirehtet.append.tips": "Примечание: Введенные аргументы будут непосредственно добавлены к команде gnirehtet без фильтрации дублирующихся аргументов.", "preferences.common.floatControl.name": "Плавающая панель управления", "preferences.common.floatControl.placeholder": "После включения плавающая панель управления устройства будет автоматически открываться во время зеркалирования", "preferences.common.auto-connect.name": "Автоподключение", "preferences.common.auto-connect.placeholder": "При включении программа попытается автоматически подключиться к историческим устройствам при запуске.", "preferences.common.auto-mirror.name": "Автозеркалирование", "preferences.common.auto-mirror.placeholder": "При включении устройства в списке устройств будут автоматически зеркалироваться.", "preferences.common.edgeHidden.name": "Автоскрытие главной панели", "preferences.common.edgeHidden.placeholder": "При включении главная панель будет автоматически скрываться, когда курсор приближается к краю экрана", "preferences.common.edgeHidden.tips": "Примечание: Изменения вступят в силу после перезапуска приложения", "preferences.common.imeFix.name": "Исправление клавиатуры при запуске приложения", "preferences.common.imeFix.placeholder": "При включении будет решена проблема, когда метод ввода не отображается в текущем зеркальном окне при запуске приложения.", "preferences.common.imeFix.tips": "Примечание: Эта функция поддерживается только в scrcpy v3.2 и выше. Использование в более ранних версиях вызовет ошибки.", "preferences.video.name": "Видео", "preferences.video.disable-video.name": "Отключить передачу видео", "preferences.video.disable-video.placeholder": "При включении передача видео будет отключена", "preferences.video.video-source.name": "Источник видео", "preferences.video.video-source.placeholder": "Дисплей устройства", "preferences.video.video-source.display": "Дис<PERSON><PERSON>ей", "preferences.video.video-source.camera": "Камера", "preferences.video.resolution.name": "Максимальный размер", "preferences.video.resolution.placeholder": "Размер устройства, формат: 1080", "preferences.video.bit.name": "Битрейт видео", "preferences.video.bit.placeholder": "8000000, формат: 8M, 8000000", "preferences.video.video-code.name": "Кодек видео", "preferences.video.video-code.placeholder": "h.264", "preferences.video.refresh-rate.name": "Частота кадров", "preferences.video.refresh-rate.placeholder": "60", "preferences.video.display-orientation.name": "Ориентация дисплея", "preferences.video.display-orientation.placeholder": "Ориентация устройства", "preferences.video.angle.name": "Угол поворота", "preferences.video.angle.placeholder": "Без поворота, формат: 15", "preferences.video.angle.tips": "Примечание: Этот параметр также действует при записи", "preferences.video.screen-cropping.name": "Обрезка", "preferences.video.screen-cropping.placeholder": "Без обрезки, формат: 1224:1440:0:0", "preferences.video.display.name": "Дис<PERSON><PERSON>ей", "preferences.video.display.placeholder": "Основной дисплей", "preferences.video.video-buffer.name": "Видео буфер", "preferences.video.video-buffer.placeholder": "0", "preferences.video.receiver-buffer.name": "Буфер приемника (v412)", "preferences.video.receiver-buffer.placeholder": "0", "preferences.device.name": "Устройство", "preferences.device.show-touch.name": "Показать касания", "preferences.device.show-touch.placeholder": "Включить точки обратной связи при касании", "preferences.device.show-touch.tips": "Только физическое устройство", "preferences.device.stay-awake.name": "Не выключать экран", "preferences.device.stay-awake.placeholder": "Предотвратить переход устройства в спящий режим", "preferences.device.stay-awake.tips": "Только проводное подключение", "preferences.device.turnScreenOff.name": "Выключить экран", "preferences.device.turnScreenOff.placeholder": "Выключить экран устройства во время управления", "preferences.device.screenOffTimeout.name": "Тайм-аут экрана", "preferences.device.screenOffTimeout.placeholder": "По умолчанию устройства", "preferences.device.screenOffTimeout.tips": "Измените настройку тайм-аута экрана и восстановите значение по умолчанию устройства при выходе", "preferences.device.control-end-video.name": "Выключить экран при завершении", "preferences.device.control-end-video.placeholder": "Выключить экран при завершении управления", "preferences.device.control-in-stop-charging.name": "Остановить зарядку", "preferences.device.control-in-stop-charging.placeholder": "Остановить зарядку при управлении", "preferences.device.control-in-stop-charging.tips": "Остановить зарядку при управлении", "preferences.device.display-overlay.name": "Эмуляция дисплея", "preferences.device.display-overlay.placeholder": "Размер устройства, Формат: 1920x1080/420, 1920x1080, /240", "preferences.device.display-overlay.tips": "Используется для настройки размера и разрешения симулируемых вспомогательных дисплеев, запуск приложений и многоэкранное взаимодействие (группа зеркалирования) зависят от этой опции", "preferences.window.name": "Окно", "preferences.window.borderless.name": "Без рамки", "preferences.window.borderless.placeholder": "Окно управления без рамки", "preferences.window.full-screen.name": "Полноэкранный режим", "preferences.window.full-screen.placeholder": "Полноэкранное окно управления", "preferences.window.always-top.name": "Всегда сверху", "preferences.window.always-top.placeholder": "Держать окно управления поверх остальных", "preferences.window.disable-screen-saver.name": "Отключить экранную заставку", "preferences.window.disable-screen-saver.placeholder": "Отключить экранную заставку компьютера", "preferences.window.size.width": "<PERSON>и<PERSON><PERSON>на окна", "preferences.window.size.width.placeholder": "<PERSON>и<PERSON><PERSON>на устройства", "preferences.window.size.width.tips": "Примечание: Изменение этой настройки может привести к размытию изображения.", "preferences.window.size.height": "Высота окна", "preferences.window.size.height.placeholder": "Высота устройства", "preferences.window.size.height.tips": "Примечание: Изменение этой настройки может привести к размытию изображения.", "preferences.window.position.x": "Позиция окна по X", "preferences.window.position.x.placeholder": "Относительно центра рабочего стола", "preferences.window.position.y": "Позиция окна по Y", "preferences.window.position.y.placeholder": "Относительно центра рабочего стола", "preferences.record.name": "Запись", "preferences.record.format.name": "Формат", "preferences.record.format.placeholder": "mp4", "preferences.record.format.audio.name": "Аудио формат", "preferences.record.format.audio.placeholder": "opus", "preferences.record.time-limit.name": "Ограничение времени записи", "preferences.record.time-limit.placeholder": "Без ограничения времени", "preferences.record.orientation.name": "Ориентация видео", "preferences.record.orientation.placeholder": "Ориентация устройства", "preferences.record.no-video-playback.name": "Отключить воспроизведение видео", "preferences.record.no-video-playback.placeholder": "При включении воспроизведение видео будет отключено во время записи", "preferences.record.no-video-playback.tips": "Примечание: Видео все равно будет записано, просто воспроизведение отключено", "preferences.record.no-audio-playback.name": "Отключить воспроизведение аудио", "preferences.record.no-audio-playback.placeholder": "При включении воспроизведение аудио будет отключено во время записи", "preferences.record.no-audio-playback.tips": "Примечание: Аудио все равно будет записано, просто воспроизведение отключено", "preferences.audio.name": "Аудио", "preferences.audio.disable-audio.name": "Отключить передачу аудио", "preferences.audio.disable-audio.placeholder": "При включении передача аудио будет отключена", "preferences.audio.disable-audio.tips": "Если захват аудио на вашем устройстве работает некорректно, вы можете включить эту опцию, чтобы обеспечить нормальное отображение зеркалирования", "preferences.audio.audioDup.name": "Сохранить аудио устройства", "preferences.audio.audioDup.placeholder": "При включении аудио будет продолжать воспроизводиться на устройстве во время зеркалирования", "preferences.audio.audioDup.tips": "Примечание: Эта опция требует Android 13+ и приложения могут отказаться (в этом случае они не будут захвачены)", "preferences.audio.audio-source.name": "Источник аудио", "preferences.audio.audio-source.placeholder": "Аудиовыход устройства", "preferences.audio.audio-source.tips": "Совет: Выбор 'Микрофон' в качестве источника позволит вам записывать аудио.", "preferences.audio.audio-source.output": "Выход", "preferences.audio.audio-source.mic": "Микрофон", "preferences.audio.audio-source.playback": "Захват аудио воспроизведения (приложения для Android могут отказаться от этого, поэтому может не захватываться весь вывод)", "preferences.audio.audio-source.mic-unprocessed": "Захват необработанного (сырого) звука с микрофона", "preferences.audio.audio-source.mic-camcorder": "Захват микрофона, настроенного для видеозаписи, если доступно, его направление совпадает с направлением камеры", "preferences.audio.audio-source.mic-voice-recognition": "Захват микрофона, настроенного для распознавания голоса", "preferences.audio.audio-source.mic-voice-communication": "Захват микрофона, настроенного для голосовой связи (например, если доступно, будет использоваться эхо-отмена или автоматическое управление усилением)", "preferences.audio.audio-source.voice-call": "Захват голосового вызова", "preferences.audio.audio-source.voice-call-uplink": "Захват только uplink голосового вызова", "preferences.audio.audio-source.voice-call-downlink": "Захват только downlink голосового вызова", "preferences.audio.audio-source.voice-performance": "Захват аудио для живых выступлений (караоке), включая микрофон и воспроизведение устройств", "preferences.audio.audio-code.name": "Кодек аудио", "preferences.audio.audio-code.placeholder": "opus", "preferences.audio.audio-bit-rate.name": "Битрейт аудио", "preferences.audio.audio-bit-rate.placeholder": "128000, формат: 128K, 128000", "preferences.audio.audio-bit-rate.tips": "Примечание: Эта опция не применяется к аудиокодекам RAW.", "preferences.audio.audio-buffer.name": "<PERSON>у<PERSON>и<PERSON> буфер", "preferences.audio.audio-buffer.placeholder": "0", "preferences.audio.audio-output-buffer.name": "Буфер аудио вывода", "preferences.audio.audio-output-buffer.placeholder": "5", "preferences.input.name": "Ввод", "preferences.input.mouse.name": "Режим мыши", "preferences.input.mouse.placeholder": "sdk", "preferences.input.mouse.tips": "Установить режим ввода мыши", "preferences.input.mouse.options[0].placeholder": "По умолчанию", "preferences.input.mouse.options[1].placeholder": "Эмулирует физическую HID-мышь с использованием модуля ядра UHID на устройстве", "preferences.input.mouse.options[2].placeholder": "Эмулирует физическую HID-мышь с использованием протокола AOAv2", "preferences.input.mouse.options[3].placeholder": "Отключить ввод мыши", "preferences.input.mouse-bind.name": "Привязка мыши", "preferences.input.mouse-bind.tips": "Эта опция позволяет настраивать функции кнопок мыши. Она использует два набора последовательностей по 4 символа для определения основных и вторичных (клавиша Shift) привязок. Каждый символ представляет кнопку мыши (правая, средняя, 4-я, 5-я) и может быть установлен в: '+' передать на устройство, '-' игнорировать, 'b' назад, 'h' домой, 's' переключение приложений, 'n' раскрыть панель уведомлений. Например, --mouse-bind=bhsn:++++ означает, что основные привязки - назад/домой/переключение приложений/уведомления, а вторичные привязки все передаются на устройство.", "preferences.input.mouse-bind.placeholder": "bhsn:++++", "preferences.input.keyboard.name": "Режим клавиатуры", "preferences.input.keyboard.placeholder": "sdk", "preferences.input.keyboard.tips": "Установить режим ввода клавиатуры", "preferences.input.keyboard.options[0].placeholder": "По умолчанию", "preferences.input.keyboard.options[1].placeholder": "Эмулирует физическую HID-клавиатуру с использованием модуля ядра UHID на устройстве", "preferences.input.keyboard.options[2].placeholder": "Эмулирует физическую HID-клавиатуру с использованием протокола AOAv2", "preferences.input.keyboard.options[3].placeholder": "Отключить ввод с клавиатуры", "preferences.input.keyboard.inject.name": "Ввод с клавиатуры", "preferences.input.keyboard.inject.placeholder": "по умолчанию", "preferences.input.keyboard.inject.tips": "Установить первый вариант инъекции текста с клавиатуры", "preferences.input.keyboard.inject.options[0].placeholder": "Вводить буквы как текст", "preferences.input.keyboard.inject.options[1].placeholder": "Всегда принудительно вводить исходное событие кнопки", "preferences.input.gamepad.name": "Геймпад", "preferences.input.gamepad.placeholder": "Отключено", "preferences.input.gamepad.tips": "Эта опция позволяет подключить геймпад (PS4/PS5 или Xbox) к вашему компьютеру для игры в Android-игры. Примечание: Игра должна поддерживать ввод с геймпада.", "preferences.camera.name": "Камера", "preferences.camera.camera-facing.name": "Источник камеры", "preferences.camera.camera-facing.placeholder": "Источник камеры устройства", "preferences.camera.camera-facing.front": "Фронтальная камера", "preferences.camera.camera-facing.back": "Задняя камера", "preferences.camera.camera-facing.external": "Внешняя камера", "preferences.camera.camera-size.name": "Размер камеры", "preferences.camera.camera-size.placeholder": "Размер камеры устройства, формат: 1920x1080", "preferences.camera.camera-ar.name": "Соотношение сторон камеры", "preferences.camera.camera-ar.placeholder": "Соотношение сторон камеры устройства, формат: 4:3, sensor, 1.6 и т.д.", "preferences.camera.camera-fps.name": "Частота кадров камеры", "preferences.camera.camera-fps.placeholder": "Частота кадров камеры устройства", "about.name": "О программе", "about.description": "📱 Отображайте и управляйте своим Android-устройством с помощью scrcpy в графическом режиме, созданный на базе Electron.", "about.update": "Проверить обновления", "about.update-not-available": "Установлена последняя версия", "about.update-error.title": "Ошибка проверки обновления", "about.update-error.message": "Возможно, требуется прокси. Загрузить вручную со страницы релизов?", "about.update-downloaded.title": "Загружена новая версия", "about.update-downloaded.message": "Перезапустить для обновления сейчас?", "about.update-downloaded.confirm": "Обновить", "about.update-available.title": "Доступно обновление", "about.update-available.confirm": "Обновить", "about.update.progress": "Обновление...", "about.donate.title": "Пожертвование", "about.donate.description": "Если этот проект помог вам, вы можете угостить меня кофе, чтобы у меня было больше энергии для его улучшения 😛", "about.docs.name": "Документация", "desktop.shortcut.add": "Добавить на рабочий стол"}