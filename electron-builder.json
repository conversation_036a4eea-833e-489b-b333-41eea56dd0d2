{"$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json", "productName": "Escrcpy", "appId": "org.viarotel.escrcpy", "directories": {"output": "dist-release", "buildResources": "electron/resources/build"}, "files": ["dist", "dist-electron"], "win": {"icon": "logo.ico", "target": [{"target": "nsis", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}, {"target": "portable", "arch": ["x64", "arm64"]}, {"target": "appx", "arch": ["x64", "arm64"]}], "artifactName": "${productName}-${version}-win-${arch}.${ext}", "extraResources": {"from": "electron/resources/extra", "to": "extra", "filter": ["common", "win", "win-${arch}"]}}, "nsis": {"artifactName": "${productName}-${version}-win-setup-${arch}.${ext}", "shortcutName": "${productName}", "uninstallDisplayName": "${productName}", "oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false}, "portable": {"artifactName": "${productName}-${version}-win-portable-${arch}.${ext}", "requestExecutionLevel": "user"}, "appx": {"identityName": "viarotel.Escrcpy", "publisher": "CN=E67CC10B-A1BE-413C-BC3B-6E1137E0742D", "publisherDisplayName": "viarotel", "languages": ["zh-CN", "en-US", "zh-TW"]}, "mac": {"icon": "logo.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "artifactName": "${productName}-${version}-mac-${arch}.${ext}", "extraResources": {"from": "electron/resources/extra", "to": "extra", "filter": ["common", "mac", "mac-${arch}"]}, "x64ArchFiles": "Contents/Resources/extra/**/*", "entitlementsInherit": "entitlements.mac.plist", "extendInfo": {"NSDocumentsFolderUsageDescription": "Application requests access to the user's Documents folder.", "NSDownloadsFolderUsageDescription": "Application requests access to the user's Downloads folder."}, "type": "development", "notarize": false, "darkModeSupport": true, "hardenedRuntime": false}, "linux": {"icon": "logo.icns", "maintainer": "<EMAIL>", "category": "Utility", "target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}], "artifactName": "${productName}-${version}-linux-${arch}.${ext}", "extraResources": {"from": "electron/resources/extra", "to": "extra", "filter": ["common", "linux", "linux-${arch}"]}}, "asar": true, "npmRebuild": true, "publish": {"provider": "github", "owner": "viarotel-org", "repo": "escrcpy", "updaterCacheDirName": "escrcpy-updater"}}