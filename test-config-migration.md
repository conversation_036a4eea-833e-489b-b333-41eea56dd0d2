# 配置迁移功能测试指南

## 功能概述
在 `src/pages/device/components/RemoveAction/index.vue` 中实现了配置迁移功能，当用户尝试删除设备时：

1. **配置检测**：使用 `ScrcpyConfigMigrator` 检查设备是否存在 scrcpy 配置
2. **弹窗交互**：如果存在配置，显示迁移对话框
3. **多选设备**：用户可以选择多个目标设备进行配置迁移
4. **操作按钮**：取消、跳过、开始迁移
5. **国际化支持**：支持中文、英文、日文、繁体中文

## 测试步骤

### 1. 准备测试环境
- 确保有多个设备连接（或历史设备记录）
- 确保某些设备有 scrcpy 配置

### 2. 测试配置检测和目标设备判断
1. 打开设备列表页面
2. 找到一个离线状态的设备（显示删除按钮）
3. 点击删除按钮
4. 如果设备没有配置，应该直接删除
5. 如果设备有配置但没有可迁移的目标设备，应该直接删除
6. 如果设备有配置且有可迁移的目标设备，应该弹出迁移对话框

### 3. 测试迁移对话框
1. 验证对话框标题和描述文案
2. 检查是否正确显示可迁移的目标设备
3. 测试多选功能
4. 验证按钮状态（开始迁移按钮在未选择设备时应该禁用）

### 4. 测试迁移功能
1. 选择一个或多个目标设备
2. 点击"开始迁移"
3. 验证迁移成功消息
4. 检查配置是否正确迁移到目标设备

### 5. 测试国际化
1. 切换到不同语言
2. 验证所有文案是否正确翻译
3. 检查对话框布局是否正常

## 实现文件

### 主要文件
- `src/pages/device/components/RemoveAction/index.vue` - 主组件
- `src/pages/device/components/RemoveAction/ConfigMigrationDialog.vue` - 迁移对话框

### 国际化文件
- `src/locales/languages/zh-CN.json` - 中文翻译
- `src/locales/languages/en-US.json` - 英文翻译
- `src/locales/languages/ja-JP.json` - 日文翻译
- `src/locales/languages/zh-TW.json` - 繁体中文翻译

## 关键功能点

### 配置检测和目标设备判断逻辑
```javascript
const migrator = new ScrcpyConfigMigrator()
const scrcpyConfig = migrator.getScrcpyConfig()
const hasConfig = migrator.hasConfig(scrcpyConfig, device.id)

if (hasConfig) {
  const targetDevices = this.findTargetDevices(device)
  if (targetDevices.length > 0) {
    // 显示迁移对话框
  } else {
    // 直接删除设备
  }
}
```

### 目标设备查找
通过 `serialNo` 匹配同一设备的不同连接实例：
```javascript
findTargetDevices(device) {
  if (!device?.serialNo) {
    return []
  }

  return this.deviceStore.list.filter(targetDevice => {
    return targetDevice.serialNo === device.serialNo &&
           targetDevice.id !== device.id
  })
}
```

### 配置迁移执行
```javascript
const success = migrator.migrateConfigFromOldToNew(
  this.sourceDevice.id,
  targetDeviceId
)
```

## 注意事项
1. 只有离线状态的设备才会显示删除按钮
2. 配置迁移基于设备的 `serialNo` 进行匹配
3. 在打开迁移对话框前会先检查是否有可迁移的目标设备
4. 如果没有可迁移的目标设备，即使有配置也会直接删除设备
5. 迁移成功后会显示成功消息并删除原设备
6. 用户可以选择跳过迁移直接删除设备
7. 取消操作不会删除设备
