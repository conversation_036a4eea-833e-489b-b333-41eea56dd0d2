# Escrcpy

### 电脑连接后无法识别设备

1. 请重新插拔设备，并确保设备已授权USB调试权限。
2. 若仍无法识别，可能是电脑缺少必要驱动。请使用驱动精灵等第三方工具安装驱动后重试。

### 无法输入中文

> 在Scrcpy@2.4+及以上版本中，解决方案如下：

1. Escrcpy设置：进入`偏好设置` → `输入控制` → `键盘模式`，选择`uhid`模式。
2. 设备输入法准备：安装支持物理键盘的输入法（推荐微信输入法）并完成设置。
3. 启动镜像：点击Escrcpy中的`开始镜像`。验证：设备的`设置` → `系统` → `语言与输入`中应显示`物理键盘`和`屏幕键盘`选项。
4. 设备输入设置：在`屏幕键盘`设置中启用微信输入法。在`物理键盘`设置中配置键盘布局与电脑键盘一致（仅需设置一次）。
5. 电脑输入准备：将输入模式设置为英文（重要）。
6. 切换输入语言：使用`Ctrl` + `Shift`切换中英文。
7. 开始使用。

[下载微信输入法](https://z.weixin.qq.com/)

### 无线连接提示：目标计算机积极拒绝访问

首次无线连接可能需要配对。或插入USB确保连接建立并授权成功后再使用无线。

### 数据线连接后点击无线模式无响应

请再次点击，或点击刷新设备。通常不会超过两次点击。若仍无效，请提交设备型号和安卓版本至[Issues](https://github.com/viarotel-org/escrcpy/issues)

### 为何设备交互控制栏未设计为自动贴边的悬浮菜单？

需注意，原则上Escrcpy只是基于Scrcpy的GUI版本，尽管扩展了部分功能，但这些扩展不影响Scrcpy核心。实现该特性需修改底层Scrcpy代码，这会导致Escrcpy更难同步Scrcpy更新，弊大于利。

因此，经慎重考虑，我们决定采用现有方案，并期待Scrcpy未来原生支持交互控制栏。

### 部分设备连接后可见画面但无法操作

> 注：小米手机尤其需注意，除开启USB调试外，还需开启USB调试（安全设置），即允许通过USB调试修改权限或模拟点击。

详细说明请参考[鼠标键盘无法工作的原因](https://github.com/Genymobile/scrcpy/blob/master/FAQ.md#mouse-and-keyboard-do-not-work)

### 下载时提示杀毒检测导致无法正常下载

> 经反馈，因缺少证书签名，Windows Defender偶会拦截软件包下载。可尝试以下方案：

1. 打开`Windows安全中心`。
2. 选择`病毒和威胁防护`。
3. 在`病毒和威胁防护设置`中点击`管理设置`。
4. 找到`实时保护`，若权限允许可尝试点击关闭。若无法关闭实时保护，请跳过此步。
5. 向下滚动页面，找到`排除项`，点击`添加或删除排除项`。
6. 将下载软件包的文件夹路径添加为排除项，即加入`排除列表`。

### 启动镜像/录制时获取设备列表失败或报错

> 通常由`Adb`或`Scrcpy`路径错误引起，可尝试以下方案：

1. 在菜单中选择`偏好设置`，点击`全局模式`右上角的重置配置按钮。
2. 进入`设备列表`页面重试启用镜像。
3. 确保已下载安装最新版`Escrcpy`。
4. 按`Ctrl` + `Shift` + `I`打开开发者工具检查报错信息。
5. 若有报错，截图并提交至[反馈Issues](https://github.com/viarotel-org/escrcpy/issues)页面。

### macOS窗口最小化至系统托盘图标未找到

> 通常因系统托盘图标过多溢出隐藏了Escrcpy图标。可尝试使用以下工具：

- [iBar](https://www.better365.cn/ibar.html)
- [Bartender](https://www.macbartender.com/)

### macOS安装成功后打开提示文件已损坏

> 通常因软件包未签名导致。可尝试以下方案：

1. 打开终端执行`sudo spctl --master-disable`允许任何来源软件。
2. 打开终端执行`sudo xattr -r -d com.apple.quarantine /Applications/Escrcpy.app`尝试修复损坏提示。

### 无法定位程序输入点DiscardvirtualMemory于动态链接库Kernel32.dll上

仅支持`Windows 10`及以上版本。

### 音频捕获异常导致镜像失败

> 通常因电脑缺少音频输出或安卓版本过低（Android 11+）。

请尝试通过`偏好设置`的`禁用音频转发`功能解决此问题。

### 微软商店版镜像启动报错

> 因安装目录文件缺少执行权限引起。

需自定义`scrcpy`和`adb`的文件路径（确保有执行权限）。若使用反向网络共享，需同样配置`gnirehtet`。

### 无法执行"adb start-server"

可能是安装路径包含中文或特殊字符。请尝试更改安装路径。

### Linux系统安装后无法打开

> 部分流行发行版（如Ubuntu 24.04）对AppImage应用新增了沙盒使用限制。临时解决方案如下：

```shell
sudo chmod 4755 /opt/Escrcpy/chrome-sandbox
```