/**
 * Customize default theme styling by overriding CSS variables:
 * https://github.com/vuejs/vitepress/blob/main/src/client/theme-default/styles/vars.css
 */

/**
 * Colors
 * -------------------------------------------------------------------------- */

 :root {
  --vp-c-gutter: var(--vp-c-divider);
  --vp-code-block-bg: rgb(125 125 125 / 0.04);
  --vp-code-tab-divider: var(--vp-c-divider);
  --vp-code-copy-code-bg: rgb(125 125 125 / 0.1);
  --vp-code-copy-code-hover-bg: rgb(125 125 125 / 0.2);
  --vp-c-disabled-bg: rgb(125 125 125 / 0.2);
  --vp-code-tab-text-color: var(--vp-c-text-2);
  --vp-code-tab-active-text-color: var(--vp-c-text-1);
  --vp-code-tab-hover-text-color: var(--vp-c-text-1);
  --vp-code-copy-code-active-text: var(--vp-c-text-2);
  --vp-c-text-dark-3: rgb(56 56 56 / 0.8);
  --vp-c-brand-lightest: var(--vp-c-brand-1);

  --vp-c-highlight-bg: var(--vp-c-brand-light);
  --vp-c-highlight-text: var(--vp-c-bg);
}

.dark {
  --vp-code-block-bg: rgb(0 0 0 / 0.2);
  --vp-c-text-code: #c0cec0;
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: var(--vp-c-brand-light);
  --vp-button-brand-text: var(--vp-c-white);
  --vp-button-brand-bg: var(--vp-c-brand-1);
  --vp-button-brand-hover-border: var(--vp-c-brand-light);
  --vp-button-brand-hover-text: var(--vp-c-white);
  --vp-button-brand-hover-bg: var(--vp-c-brand-light);
  --vp-button-brand-active-border: var(--vp-c-brand-light);
  --vp-button-brand-active-text: var(--vp-c-white);
  --vp-button-brand-active-bg: var(--vp-button-brand-bg);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(120deg, var(--vp-c-brand-1) 30%, var(--vp-c-brand-next));
  --vp-home-hero-image-background-image: linear-gradient(-45deg, var(--vp-c-brand-1) 30%, var(--vp-c-brand-next));
  --vp-home-hero-image-filter: blur(80px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(120px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(120px);
  }
}

/* Safari has a very bad performance on gradient and filter */
.browser-safari,
.browser-firefox {
  --vp-home-hero-image-background-image: transparent;
  --vp-home-hero-image-filter: '';
}

/**
 * Component: Custom Block
 * -------------------------------------------------------------------------- */

:root {
  --vp-custom-block-tip-border: var(--vp-c-brand-1);
  --vp-custom-block-tip-text: var(--vp-c-brand-darker);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

.dark {
  --vp-custom-block-tip-border: var(--vp-c-brand-1);
  --vp-custom-block-tip-text: var(--vp-c-brand-lightest);
  --vp-custom-block-tip-bg: var(--vp-c-brand-dimm);
}

/**
 * Component: Algolia
 * -------------------------------------------------------------------------- */

.DocSearch {
  --docsearch-primary-color: var(--vp-c-brand-1) !important;
}