---
title: Guide
---

# Escrcpy: A Graphical Android Device Control Tool Based on Electron

## What is Escrcpy?

Escrcpy is a graphical Android device control tool developed based on Electron. Built upon the renowned open-source project Scrcpy, it provides users with a more user-friendly and feature-rich graphical interface. With Escrcpy, users can easily display and control Android devices on their computers, enjoying a high-definition, low-latency screen mirroring experience.

## Core Features

### Efficient and Stable Screen Mirroring
- ⚡️ High Performance: Supports 30-120 fps (depending on device capability)  
- 🌟 High Quality: Supports 1920×1080 or higher resolution display  
- 🕒 Low Latency: Only 35~70 milliseconds of delay  
- 🚀 Fast Startup: Displays the first frame in about 1 second  

### Rich Functional Features
- 📡 Wireless Connection: Supports quick device connection via QR code scanning
- 🤖 Automation: Auto-connect devices, auto-execute mirroring, custom scripts, and scheduled tasks
- 💡 Highly Customizable: Multi-device management, independent configurations, custom notes, config import/export
- 🖥️ Window Arrangement: Visual multi-device window layout management with drag-and-drop position and size adjustment
- 🔗 Reverse Tethering: Built-in Gnirehtet reverse tethering functionality
- 🎨 Theme Switching: Supports light/dark modes, automatically switches with the system

### Optimized User Experience
- 😎 Lightweight: Native support, displays only the device screen  
- 🙅‍♂️ No Residue: Leaves no traces on Android devices  
- 🤩 Pure Experience: No account required, no ads, no internet needed  
- 🗽 Completely Free and Open Source  

## Use Cases

Escrcpy is ideal for the following scenarios:  
1. Developers debugging Android apps on their computers  
2. Gamers playing mobile games on larger screens  
3. Users needing to record mobile screen content  
4. Situations requiring phone operation on a computer for work  
5. Demonstrating phone operations during teaching sessions  

## Cross-Platform Support

Escrcpy supports major operating systems:  
- Windows  
- macOS  
- Linux  

## Technical Advantages

As the graphical interface version of Scrcpy, Escrcpy retains all the advantages of Scrcpy while adding:  
- A more intuitive device management interface  
- More convenient connection methods  
- Richer customization options  
- More comprehensive batch operation features  

## Future Development

The project team continues to improve Escrcpy, with future plans including:  
- Enhancing the way mirror window position and size are set  
- Optimizing the batch device connection experience  
- Developing a graphical script editing tool  

## How to use

Escrcpy is a completely free and open-source project. Please refer to the specific usage:  

[Get Started 👉](/guide/started)

## Conclusion

Escrcpy brings a modern graphical interface and enhanced features to Scrcpy, making Android device control simpler and more efficient. Whether you're a developer or an average user, Escrcpy can provide an outstanding device control experience. Its open-source and free nature also allows more people to use this excellent tool without hesitation.

If you're looking for a powerful, user-friendly Android device control tool, Escrcpy is definitely worth trying!