<div style="display:flex;">
  <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/electron/resources/build/logo.png" alt="viarotel-escrcpy" width="108px">
</div>

# Escrcpy

[![GitCode](https://gitcode.com/viarotel-org/escrcpy/star/badge.svg)](https://gitcode.com/viarotel-org/escrcpy)
[![Gitee](https://gitee.com/viarotel-org/escrcpy/badge/star.svg?theme=dark)](https://gitee.com/viarotel-org/escrcpy)
[![GitHub](https://img.shields.io/github/stars/viarotel-org/escrcpy?label=Github%20Stars)](https://github.com/viarotel-org/escrcpy)
[![Ask DeepWiki](https://deepwiki.com/badge.svg)](https://deepwiki.com/viarotel-org/escrcpy)

📱 Display and control your Android device graphically with scrcpy, powered by Electron. [中文文档](https://github.com/viarotel-org/escrcpy/blob/main/README-CN.md)

<div style="display:flex;">
  <img src="./screenshots/zh-CN/overview.jpg" alt="viarotel-escrcpy" width="100%">
</div>

## Features

- 🏃 Synchronization: Faster synchronization with Scrcpy thanks to web technology
- 🤖 Automation: Auto-connect devices, auto-execute mirroring, custom scripts, scheduled tasks
- 💡 Customization: Multi-device management, independent configurations, custom notes, config import/export
- 📡 Wireless: Quick connection via QR code scanning
- 🔗 Reverse Tethering: Gnirehtet reverse tethering
- 🎨 Themes: Light mode, dark mode, system theme following
- 😎 Lightweight: Native support, displays only the device screen
- ⚡️ Performance: 30~120 FPS, depending on the device
- 🌟 Quality: 1920×1080 or higher
- 🕒 Low Latency: 35~70 ms
- 🚀 Fast Startup: First image displayed in about 1 second
- 🙅‍♂️ Non-intrusive: No installation files left on Android devices
- 🤩 User Benefits: No accounts, no ads, no internet connection required
- 🗽 Freedom: Free and open-source software

## Installation

### Manual Installation via Released Packages

Check the [Releases Page](https://github.com/viarotel-org/escrcpy/releases)

### macOS Installation via Homebrew

Refer to [homebrew-escrcpy](https://github.com/viarotel-org/homebrew-escrcpy)

## Documentation

- [Getting Started](https://escrcpy.viarotel.eu.org/guide/started)
- [Shortcuts](https://escrcpy.viarotel.eu.org/reference/scrcpy/shortcuts)
- [Device Operations](https://escrcpy.viarotel.eu.org/guide/operation)
- [Preferences](https://escrcpy.viarotel.eu.org/guide/preferences)
- [Reverse Tethering](https://escrcpy.viarotel.eu.org/reference/gnirehtet/)

## For Developers

If you are a developer and wish to run or help improve this project, refer to the [Development Documentation](https://github.com/viarotel-org/escrcpy/blob/main/develop.md)

## Get Help

As an open-source project powered by passion, support is limited, and updates are irregular.

- [FAQ](https://escrcpy.viarotel.eu.org/help/escrcpy)
- [Report Issues](https://github.com/viarotel-org/escrcpy/issues)
- [Contact Email](<EMAIL>)

## What's Next?

[Milestones](https://escrcpy.viarotel.eu.org/guide/milestones)

## Acknowledgments

This project owes its existence to the following open-source projects:

- [scrcpy](https://github.com/Genymobile/scrcpy)
- [adbkit](https://github.com/DeviceFarmer/adbkit)
- [electron](https://www.electronjs.org/)
- [vue](https://vuejs.org/)
- [gnirehtet](https://github.com/Genymobile/gnirehtet/)

## Donate

If this project has helped you, consider buying me a coffee to keep me motivated for further improvements 😛

<div style="display:flex;">
  <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/src/assets/sponsor/viarotel-wepay.png" alt="viarotel-wepay" width="30%">
  <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/src/assets/sponsor/viarotel-alipay.png" alt="viarotel-alipay" width="30%">
  <a href="https://www.paypal.com/paypalme/viarotel" target="_blank" rel="noopener noreferrer">
    <img src="https://cdn.jsdelivr.net/gh/viarotel-org/escrcpy@main/src/assets/sponsor/viarotel-paypal.png" alt="viarotel-paypal" width="30%">
  </a>
</div>

## Contributors

Thanks to all who contributed!

[Contributors](https://github.com/viarotel/escrcpy/graphs/contributors)

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=viarotel-org/escrcpy&type=Date)](https://star-history.com/#viarotel-org/escrcpy&Date)